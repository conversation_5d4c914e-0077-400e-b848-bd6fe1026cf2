# 🧪 Guide de Tests - TrainTickets

Ce guide vous accompagne pour tester toutes les fonctionnalités de l'application TrainTickets.

## 🚀 Démarrage de l'Application

### 1. Lancer l'application
```bash
# Linux/Mac
./start.sh

# Windows
start.bat

# Ou directement avec Maven
mvn jetty:run
```

### 2. Vérifier l'accès
- **URL** : http://localhost:8080/train-tickets
- **Page de test** : http://localhost:8080/train-tickets/test

## 🔍 Tests Automatisés

### Lancer les tests automatisés
```bash
# Linux/Mac
chmod +x test-app.sh
./test-app.sh

# Windows
test-app.bat
```

## 📋 Tests Manuels Détaillés

### 1. 🏠 Test de la Page d'Accueil

**Objectif** : Vérifier l'affichage et la navigation

**Étapes** :
1. Accédez à http://localhost:8080/train-tickets
2. Vérifiez l'affichage du header avec navigation
3. Testez le formulaire de recherche rapide
4. Vérifiez les liens de navigation

**Résultat attendu** :
- ✅ Page d'accueil attractive avec hero section
- ✅ Navigation fonctionnelle
- ✅ Formulaire de recherche opérationnel
- ✅ Design responsive

### 2. 🔐 Test d'Authentification

#### 2.1 Inscription
**Étapes** :
1. Cliquez sur "Inscription"
2. Remplissez le formulaire avec de nouvelles données
3. Soumettez le formulaire

**Données de test** :
```
Prénom: Test
Nom: Utilisateur
Username: testuser
Email: <EMAIL>
Téléphone: +33 6 12 34 56 78
Mot de passe: password123
```

**Résultat attendu** :
- ✅ Compte créé avec succès
- ✅ Connexion automatique
- ✅ Redirection vers le dashboard

#### 2.2 Connexion
**Étapes** :
1. Déconnectez-vous
2. Cliquez sur "Connexion"
3. Utilisez les comptes de test

**Comptes de test** :
```
Admin: <EMAIL> / password123
Client: <EMAIL> / password123
Employé: <EMAIL> / password123
```

**Résultat attendu** :
- ✅ Connexion réussie
- ✅ Redirection appropriée selon le rôle
- ✅ Navigation mise à jour

### 3. 🔍 Test de Recherche de Trajets

**Étapes** :
1. Accédez à la page de recherche
2. Testez différentes combinaisons de villes
3. Ajoutez une date et un nombre de passagers

**Recherches de test** :
```
Paris → Lyon (aujourd'hui + 1 jour)
Marseille → Paris (2 passagers)
Toulouse → Bordeaux (sans date)
```

**Résultat attendu** :
- ✅ Résultats affichés avec détails
- ✅ Filtrage par date fonctionnel
- ✅ Boutons de réservation actifs
- ✅ Gestion des cas sans résultats

### 4. 🎫 Test de Réservation Complète

#### 4.1 Création de réservation
**Étapes** :
1. Connectez-<NAME_EMAIL>
2. Recherchez un trajet Paris → Lyon
3. Cliquez sur "Réserver" pour un trajet
4. Remplissez les informations passagers
5. Confirmez la réservation

**Données de test** :
```
Noms des passagers:
Jean Dupont
Marie Martin

Demandes spéciales:
Assistance mobilité réduite
```

**Résultat attendu** :
- ✅ Réservation créée avec numéro
- ✅ Statut "En attente"
- ✅ Places réservées sur le trajet

#### 4.2 Confirmation de réservation
**Étapes** :
1. Accédez aux détails de la réservation
2. Cliquez sur "Confirmer la réservation"

**Résultat attendu** :
- ✅ Statut changé en "Confirmé"
- ✅ Bouton de paiement disponible

### 5. 💳 Test du Système de Paiement

**Étapes** :
1. Depuis une réservation confirmée, cliquez "Payer maintenant"
2. Remplissez le formulaire de paiement
3. Soumettez le paiement

**Données de test** :
```
Nom du titulaire: Jean Dupont
Numéro de carte: 4111111111111111
Mois d'expiration: 12
Année d'expiration: 2025
CVV: 123
```

**Résultat attendu** :
- ✅ Paiement traité (90% de réussite simulée)
- ✅ Page de succès affichée
- ✅ Ticket marqué comme payé
- ✅ Numéro de transaction généré

### 6. 👤 Test de l'Espace Utilisateur

#### 6.1 Dashboard
**Étapes** :
1. Connectez-vous et accédez au dashboard
2. Vérifiez les statistiques
3. Consultez les réservations récentes

**Résultat attendu** :
- ✅ Statistiques correctes
- ✅ Réservations affichées
- ✅ Actions rapides fonctionnelles

#### 6.2 Gestion du profil
**Étapes** :
1. Accédez à "Mon Profil"
2. Modifiez les informations personnelles
3. Changez le mot de passe

**Résultat attendu** :
- ✅ Modifications sauvegardées
- ✅ Mot de passe changé avec succès
- ✅ Session mise à jour

### 7. 📱 Test de Responsivité

**Étapes** :
1. Testez sur différentes tailles d'écran
2. Utilisez les outils de développement du navigateur
3. Testez la navigation mobile

**Tailles à tester** :
- 📱 Mobile (375px)
- 📱 Tablette (768px)
- 💻 Desktop (1200px)

**Résultat attendu** :
- ✅ Interface adaptée à chaque taille
- ✅ Navigation mobile fonctionnelle
- ✅ Formulaires utilisables

## 🐛 Tests d'Erreurs

### 1. Test de validation
- Formulaires avec données invalides
- Champs obligatoires vides
- Formats d'email incorrects

### 2. Test de sécurité
- Accès aux pages protégées sans connexion
- Tentative d'accès aux données d'autres utilisateurs

### 3. Test de robustesse
- Recherches avec villes inexistantes
- Réservations sur trajets complets
- Paiements avec cartes invalides

## 📊 Critères de Validation

### ✅ Tests Réussis
- [ ] Page d'accueil s'affiche correctement
- [ ] Inscription/connexion fonctionnelle
- [ ] Recherche de trajets opérationnelle
- [ ] Réservation complète possible
- [ ] Système de paiement fonctionnel
- [ ] Dashboard utilisateur accessible
- [ ] Profil modifiable
- [ ] Interface responsive
- [ ] Gestion d'erreurs appropriée

### 📈 Métriques de Performance
- Temps de chargement < 2 secondes
- Recherche < 1 seconde
- Paiement < 3 secondes

## 🔧 Dépannage

### Problèmes Courants

#### Application ne démarre pas
```bash
# Vérifier Java et Maven
java -version
mvn -version

# Nettoyer et recompiler
mvn clean compile
```

#### Base de données vide
- Redémarrer l'application
- Vérifier les logs de l'InitializationServlet

#### Erreurs de paiement
- Utiliser les numéros de carte de test
- Vérifier que tous les champs sont remplis

## 📞 Support

En cas de problème :
1. Consultez les logs de l'application
2. Vérifiez la page de test : `/test`
3. Redémarrez l'application
4. Vérifiez la configuration de la base de données

---

**Bon testing ! 🚀**
