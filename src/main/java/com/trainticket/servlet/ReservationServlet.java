package com.trainticket.servlet;

import com.trainticket.model.Reservation;
import com.trainticket.model.Route;
import com.trainticket.model.User;
import com.trainticket.service.ReservationService;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Optional;

@WebServlet(name = "ReservationServlet", urlPatterns = {
    "/reservation/book", "/reservation/confirm", "/reservation/cancel", "/reservation/details"
})
public class ReservationServlet extends HttpServlet {
    
    private ReservationService reservationService;
    private RouteService routeService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationService();
        routeService = new RouteService();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String path = request.getServletPath();
        
        switch (path) {
            case "/reservation/book":
                showBookingForm(request, response, currentUser);
                break;
            case "/reservation/details":
                showReservationDetails(request, response, currentUser);
                break;
            case "/reservation/cancel":
                handleCancelReservation(request, response, currentUser);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String path = request.getServletPath();
        
        switch (path) {
            case "/reservation/book":
                handleBooking(request, response, currentUser);
                break;
            case "/reservation/confirm":
                handleConfirmReservation(request, response, currentUser);
                break;
            default:
                response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
        }
    }
    
    private void showBookingForm(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {
        
        String routeIdStr = request.getParameter("routeId");
        String passengerCountStr = request.getParameter("passengerCount");
        
        try {
            if (routeIdStr == null || routeIdStr.trim().isEmpty()) {
                throw new IllegalArgumentException("ID du trajet manquant");
            }
            
            Long routeId = Long.parseLong(routeIdStr);
            Optional<Route> routeOpt = routeService.findById(routeId);
            
            if (routeOpt.isEmpty()) {
                throw new IllegalArgumentException("Trajet non trouvé");
            }
            
            Route route = routeOpt.get();
            
            // Nombre de passagers
            int passengerCount = 1;
            if (passengerCountStr != null && !passengerCountStr.trim().isEmpty()) {
                passengerCount = Integer.parseInt(passengerCountStr);
                if (passengerCount < 1 || passengerCount > 10) {
                    throw new IllegalArgumentException("Nombre de passagers invalide");
                }
            }
            
            // Vérifier la disponibilité
            if (!routeService.isRouteAvailable(routeId, passengerCount)) {
                throw new IllegalStateException("Ce trajet n'est plus disponible pour " + passengerCount + " passager(s)");
            }
            
            request.setAttribute("route", route);
            request.setAttribute("passengerCount", passengerCount);
            request.setAttribute("currentUser", currentUser);
            
        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", e.getMessage());
            response.sendRedirect(request.getContextPath() + "/search");
            return;
        }
        
        request.getRequestDispatcher("/WEB-INF/views/reservation/book.jsp").forward(request, response);
    }
    
    private void handleBooking(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {
        
        String routeIdStr = request.getParameter("routeId");
        String passengerCountStr = request.getParameter("passengerCount");
        String passengerNames = request.getParameter("passengerNames");
        String specialRequests = request.getParameter("specialRequests");
        
        try {
            Long routeId = Long.parseLong(routeIdStr);
            int passengerCount = Integer.parseInt(passengerCountStr);
            
            // Créer la réservation
            Reservation reservation = reservationService.createReservation(
                currentUser, routeId, passengerCount, passengerNames, specialRequests);
            
            SessionUtil.setFlashMessage(request, "success", 
                "Réservation créée avec succès ! Numéro : " + reservation.getReservationNumber());
            
            response.sendRedirect(request.getContextPath() + "/reservation/details?id=" + reservation.getId());
            
        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            
            // Recharger les données pour le formulaire
            try {
                Long routeId = Long.parseLong(routeIdStr);
                Optional<Route> routeOpt = routeService.findById(routeId);
                if (routeOpt.isPresent()) {
                    request.setAttribute("route", routeOpt.get());
                    request.setAttribute("passengerCount", passengerCountStr);
                    request.setAttribute("passengerNames", passengerNames);
                    request.setAttribute("specialRequests", specialRequests);
                    request.setAttribute("currentUser", currentUser);
                }
            } catch (Exception ex) {
                // En cas d'erreur, rediriger vers la recherche
                SessionUtil.setFlashMessage(request, "error", e.getMessage());
                response.sendRedirect(request.getContextPath() + "/search");
                return;
            }
            
            request.getRequestDispatcher("/WEB-INF/views/reservation/book.jsp").forward(request, response);
        }
    }
    
    private void showReservationDetails(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {
        
        String reservationIdStr = request.getParameter("id");
        
        try {
            if (reservationIdStr == null || reservationIdStr.trim().isEmpty()) {
                throw new IllegalArgumentException("ID de réservation manquant");
            }
            
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.findUserReservations(currentUser)
                .stream()
                .filter(r -> r.getId().equals(reservationId))
                .findFirst();
            
            if (reservationOpt.isEmpty()) {
                throw new IllegalArgumentException("Réservation non trouvée");
            }
            
            Reservation reservation = reservationOpt.get();
            request.setAttribute("reservation", reservation);
            request.setAttribute("currentUser", currentUser);
            
        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", e.getMessage());
            response.sendRedirect(request.getContextPath() + "/user/reservations");
            return;
        }
        
        request.getRequestDispatcher("/WEB-INF/views/reservation/details.jsp").forward(request, response);
    }
    
    private void handleCancelReservation(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {
        
        String reservationIdStr = request.getParameter("id");
        
        try {
            if (reservationIdStr == null || reservationIdStr.trim().isEmpty()) {
                throw new IllegalArgumentException("ID de réservation manquant");
            }
            
            Long reservationId = Long.parseLong(reservationIdStr);
            
            // Vérifier que la réservation appartient à l'utilisateur
            Optional<Reservation> reservationOpt = reservationService.findUserReservations(currentUser)
                .stream()
                .filter(r -> r.getId().equals(reservationId))
                .findFirst();
            
            if (reservationOpt.isEmpty()) {
                throw new IllegalArgumentException("Réservation non trouvée");
            }
            
            boolean cancelled = reservationService.cancelReservation(reservationId);
            
            if (cancelled) {
                SessionUtil.setFlashMessage(request, "success", "Réservation annulée avec succès");
            } else {
                SessionUtil.setFlashMessage(request, "error", "Impossible d'annuler cette réservation");
            }
            
        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/user/reservations");
    }
    
    private void handleConfirmReservation(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {
        
        String reservationIdStr = request.getParameter("reservationId");
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            
            // Vérifier que la réservation appartient à l'utilisateur
            Optional<Reservation> reservationOpt = reservationService.findUserReservations(currentUser)
                .stream()
                .filter(r -> r.getId().equals(reservationId))
                .findFirst();
            
            if (reservationOpt.isEmpty()) {
                throw new IllegalArgumentException("Réservation non trouvée");
            }
            
            boolean confirmed = reservationService.confirmReservation(reservationId);
            
            if (confirmed) {
                SessionUtil.setFlashMessage(request, "success", "Réservation confirmée avec succès");
                response.sendRedirect(request.getContextPath() + "/reservation/details?id=" + reservationId);
            } else {
                SessionUtil.setFlashMessage(request, "error", "Impossible de confirmer cette réservation");
                response.sendRedirect(request.getContextPath() + "/user/reservations");
            }
            
        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", e.getMessage());
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }
}
