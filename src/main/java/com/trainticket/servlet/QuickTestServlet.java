package com.trainticket.servlet;

import com.trainticket.util.HibernateUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;

@WebServlet(name = "QuickTestServlet", urlPatterns = {"/quick-test"})
public class QuickTestServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test Rapide</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>🚀 Test Rapide de l'Application</h1>");
        
        // Test HibernateUtil
        out.println("<h2>Test HibernateUtil</h2>");
        try {
            jakarta.persistence.EntityManager em = HibernateUtil.getEntityManager();
            if (em != null) {
                out.println("<div class='success'>✅ EntityManager créé avec succès</div>");
                em.close();
                out.println("<div class='success'>✅ EntityManager fermé avec succès</div>");
            } else {
                out.println("<div class='error'>❌ EntityManager est null</div>");
            }
        } catch (Exception e) {
            out.println("<div class='error'>❌ Erreur HibernateUtil: " + e.getMessage() + "</div>");
        }
        
        // Test des classes DAO
        out.println("<h2>Test des Classes DAO</h2>");
        try {
            Class.forName("com.trainticket.dao.impl.UserDAOImpl");
            out.println("<div class='success'>✅ UserDAOImpl trouvé</div>");
            
            Class.forName("com.trainticket.dao.impl.TrainDAOImpl");
            out.println("<div class='success'>✅ TrainDAOImpl trouvé</div>");
            
            Class.forName("com.trainticket.dao.impl.RouteDAOImpl");
            out.println("<div class='success'>✅ RouteDAOImpl trouvé</div>");
            
        } catch (ClassNotFoundException e) {
            out.println("<div class='error'>❌ Classe DAO manquante: " + e.getMessage() + "</div>");
        }
        
        // Test des services
        out.println("<h2>Test des Services</h2>");
        try {
            Class.forName("com.trainticket.service.UserService");
            out.println("<div class='success'>✅ UserService trouvé</div>");
            
            Class.forName("com.trainticket.service.TrainService");
            out.println("<div class='success'>✅ TrainService trouvé</div>");
            
            Class.forName("com.trainticket.service.RouteService");
            out.println("<div class='success'>✅ RouteService trouvé</div>");
            
        } catch (ClassNotFoundException e) {
            out.println("<div class='error'>❌ Classe Service manquante: " + e.getMessage() + "</div>");
        }
        
        out.println("<h2>📋 Actions</h2>");
        out.println("<p>Si tous les tests passent, essayez :</p>");
        out.println("<ul>");
        out.println("<li><a href='" + request.getContextPath() + "/register'>Test Inscription</a></li>");
        out.println("<li><a href='" + request.getContextPath() + "/login'>Test Connexion</a></li>");
        out.println("<li><a href='" + request.getContextPath() + "/db-test'>Test Base de Données</a></li>");
        out.println("</ul>");
        
        out.println("<div style='margin-top: 20px;'>");
        out.println("<a href='" + request.getContextPath() + "/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Retour Accueil</a>");
        out.println("</div>");
        
        out.println("</body>");
        out.println("</html>");
    }
}
