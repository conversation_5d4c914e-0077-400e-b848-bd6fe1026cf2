package com.trainticket.servlet;

import com.trainticket.model.User;
import com.trainticket.model.UserRole;
import com.trainticket.service.UserService;
import com.trainticket.service.TrainService;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@WebServlet(name = "AdminDashboardServlet", urlPatterns = {"/admin/dashboard"})
public class AdminDashboardServlet extends HttpServlet {

    private UserService userService;
    private TrainService trainService;
    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        userService = new UserService();
        trainService = new TrainService();
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier que l'utilisateur est connecté et est admin
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        if (currentUser.getRole() != UserRole.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès refusé. Droits administrateur requis.");
            return;
        }

        try {
            // Statistiques générales
            long totalUsers = userService.getTotalUsersCount();
            long totalTrains = trainService.getTotalTrainsCount();
            long totalRoutes = routeService.getTotalRoutesCount();
            long activeRoutes = routeService.getActiveRoutesCount();

            // Statistiques par rôle
            long adminCount = userService.getUserCountByRole(UserRole.ADMIN);
            long employeeCount = userService.getUserCountByRole(UserRole.EMPLOYEE);
            long clientCount = userService.getUserCountByRole(UserRole.CLIENT);

            // Utilisateurs récents
            java.util.List<User> recentUsers = userService.getRecentUsers(5);

            // Routes populaires
            java.util.List<com.trainticket.model.Route> popularRoutes = routeService.getPopularRoutes(5);

            // Préparer les données pour la vue
            request.setAttribute("currentUser", currentUser);
            request.setAttribute("totalUsers", totalUsers);
            request.setAttribute("totalTrains", totalTrains);
            request.setAttribute("totalRoutes", totalRoutes);
            request.setAttribute("activeRoutes", activeRoutes);
            request.setAttribute("adminCount", adminCount);
            request.setAttribute("employeeCount", employeeCount);
            request.setAttribute("clientCount", clientCount);
            request.setAttribute("recentUsers", recentUsers);
            request.setAttribute("popularRoutes", popularRoutes);

            // Calculer des pourcentages
            if (totalUsers > 0) {
                request.setAttribute("adminPercentage", Math.round((adminCount * 100.0) / totalUsers));
                request.setAttribute("employeePercentage", Math.round((employeeCount * 100.0) / totalUsers));
                request.setAttribute("clientPercentage", Math.round((clientCount * 100.0) / totalUsers));
            }

            if (totalRoutes > 0) {
                request.setAttribute("activeRoutesPercentage", Math.round((activeRoutes * 100.0) / totalRoutes));
            }

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des statistiques : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
