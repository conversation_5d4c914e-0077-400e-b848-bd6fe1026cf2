package com.trainticket.service;

import com.trainticket.dao.UserDAO;
import com.trainticket.dao.impl.UserDAOImpl;
import com.trainticket.model.User;
import com.trainticket.model.UserRole;
import com.trainticket.util.PasswordUtil;

import java.util.List;
import java.util.Optional;

public class UserService {

    private final UserDAO userDAO;

    public UserService() {
        this.userDAO = new UserDAOImpl();
    }

    public UserService(UserDAO userDAO) {
        this.userDAO = userDAO;
    }

    /**
     * Authentifie un utilisateur avec son nom d'utilisateur/email et mot de passe
     */
    public Optional<User> authenticate(String usernameOrEmail, String password) {
        if (usernameOrEmail == null || password == null) {
            return Optional.empty();
        }

        Optional<User> userOpt = userDAO.findByUsernameOrEmail(usernameOrEmail.trim());

        if (userOpt.isPresent()) {
            User user = userOpt.get();

            if (!user.getIsActive()) {
                return Optional.empty();
            }

            if (PasswordUtil.verifyPassword(password, user.getPassword())) {
                return Optional.of(user);
            }
        }

        return Optional.empty();
    }

    /**
     * Enregistre un nouvel utilisateur
     */
    public User registerUser(String username, String email, String password,
                           String firstName, String lastName, String phoneNumber) {

        // Validation des données
        validateUserData(username, email, password, firstName, lastName);

        // Vérifier que l'utilisateur n'existe pas déjà
        if (userDAO.existsByUsername(username)) {
            throw new IllegalArgumentException("Ce nom d'utilisateur existe déjà");
        }

        if (userDAO.existsByEmail(email)) {
            throw new IllegalArgumentException("Cet email est déjà utilisé");
        }

        // Créer le nouvel utilisateur
        User user = new User();
        user.setUsername(username.trim());
        user.setEmail(email.trim().toLowerCase());
        user.setPassword(PasswordUtil.hashPassword(password));
        user.setFirstName(firstName.trim());
        user.setLastName(lastName.trim());
        user.setPhoneNumber(phoneNumber != null ? phoneNumber.trim() : null);
        user.setRole(UserRole.CLIENT);
        user.setIsActive(true);

        return userDAO.save(user);
    }

    /**
     * Met à jour le profil d'un utilisateur
     */
    public User updateUserProfile(Long userId, String firstName, String lastName,
                                String email, String phoneNumber) {

        Optional<User> userOpt = userDAO.findById(userId);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("Utilisateur non trouvé");
        }

        User user = userOpt.get();

        // Vérifier que l'email n'est pas déjà utilisé par un autre utilisateur
        if (!user.getEmail().equals(email.trim().toLowerCase())) {
            if (userDAO.existsByEmail(email)) {
                throw new IllegalArgumentException("Cet email est déjà utilisé");
            }
            user.setEmail(email.trim().toLowerCase());
        }

        user.setFirstName(firstName.trim());
        user.setLastName(lastName.trim());
        user.setPhoneNumber(phoneNumber != null ? phoneNumber.trim() : null);

        return userDAO.update(user);
    }

    /**
     * Change le mot de passe d'un utilisateur
     */
    public boolean changePassword(Long userId, String currentPassword, String newPassword) {
        Optional<User> userOpt = userDAO.findById(userId);
        if (userOpt.isEmpty()) {
            return false;
        }

        User user = userOpt.get();

        // Vérifier l'ancien mot de passe
        if (!PasswordUtil.verifyPassword(currentPassword, user.getPassword())) {
            return false;
        }

        // Valider le nouveau mot de passe
        if (!PasswordUtil.isValidPassword(newPassword)) {
            throw new IllegalArgumentException("Le nouveau mot de passe ne respecte pas les critères de sécurité");
        }

        // Mettre à jour le mot de passe
        user.setPassword(PasswordUtil.hashPassword(newPassword));
        userDAO.update(user);

        return true;
    }

    /**
     * Trouve un utilisateur par son ID
     */
    public Optional<User> findById(Long id) {
        return userDAO.findById(id);
    }

    /**
     * Trouve un utilisateur par son nom d'utilisateur
     */
    public Optional<User> findByUsername(String username) {
        return userDAO.findByUsername(username);
    }

    /**
     * Trouve un utilisateur par son email
     */
    public Optional<User> findByEmail(String email) {
        return userDAO.findByEmail(email);
    }

    /**
     * Trouve tous les utilisateurs par rôle
     */
    public List<User> findByRole(UserRole role) {
        return userDAO.findByRole(role);
    }

    /**
     * Trouve tous les utilisateurs actifs
     */
    public List<User> findActiveUsers() {
        return userDAO.findActiveUsers();
    }

    /**
     * Recherche des utilisateurs par nom
     */
    public List<User> searchUsersByName(String name) {
        return userDAO.findByNameContaining(name);
    }

    /**
     * Désactive un utilisateur
     */
    public void deactivateUser(Long userId) {
        Optional<User> userOpt = userDAO.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setIsActive(false);
            userDAO.update(user);
        }
    }

    /**
     * Active un utilisateur
     */
    public void activateUser(Long userId) {
        Optional<User> userOpt = userDAO.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setIsActive(true);
            userDAO.update(user);
        }
    }

    /**
     * Change le rôle d'un utilisateur (admin seulement)
     */
    public void changeUserRole(Long userId, UserRole newRole) {
        Optional<User> userOpt = userDAO.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setRole(newRole);
            userDAO.update(user);
        }
    }

    /**
     * Compte le nombre total d'utilisateurs
     */
    public long getTotalUsersCount() {
        return userDAO.count();
    }

    /**
     * Compte le nombre d'utilisateurs par rôle
     */
    public long getUserCountByRole(UserRole role) {
        return userDAO.countByRole(role);
    }

    /**
     * Récupère les utilisateurs récents
     */
    public List<User> getRecentUsers(int limit) {
        return userDAO.findRecentUsers(limit);
    }

    /**
     * Valide les données utilisateur
     */
    private void validateUserData(String username, String email, String password,
                                String firstName, String lastName) {

        if (username == null || username.trim().length() < 3) {
            throw new IllegalArgumentException("Le nom d'utilisateur doit contenir au moins 3 caractères");
        }

        if (email == null || !email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new IllegalArgumentException("Format d'email invalide");
        }

        if (!PasswordUtil.isValidPassword(password)) {
            throw new IllegalArgumentException("Le mot de passe doit contenir au moins 6 caractères, une lettre et un chiffre");
        }

        if (firstName == null || firstName.trim().isEmpty()) {
            throw new IllegalArgumentException("Le prénom est obligatoire");
        }

        if (lastName == null || lastName.trim().isEmpty()) {
            throw new IllegalArgumentException("Le nom est obligatoire");
        }
    }
}
