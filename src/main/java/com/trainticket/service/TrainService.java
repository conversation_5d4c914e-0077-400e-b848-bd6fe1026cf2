package com.trainticket.service;

import com.trainticket.dao.TrainDAO;
import com.trainticket.model.Train;
import com.trainticket.model.TrainType;

import java.util.List;
import java.util.Optional;

public class TrainService {
    
    private TrainDAO trainDAO;
    
    public TrainService() {
        this.trainDAO = new TrainDAO();
    }
    
    /**
     * Trouve tous les trains
     */
    public List<Train> findAllTrains() {
        return trainDAO.findAll();
    }
    
    /**
     * Trouve tous les trains actifs
     */
    public List<Train> findActiveTrains() {
        return trainDAO.findActiveTrains();
    }
    
    /**
     * Trouve un train par son ID
     */
    public Optional<Train> findById(Long id) {
        return trainDAO.findById(id);
    }
    
    /**
     * Trouve un train par son numéro
     */
    public Optional<Train> findByTrainNumber(String trainNumber) {
        return trainDAO.findByTrainNumber(trainNumber);
    }
    
    /**
     * Trouve les trains par type
     */
    public List<Train> findByType(TrainType type) {
        return trainDAO.findByType(type);
    }
    
    /**
     * Sauvegarde un train
     */
    public Train saveTrain(Train train) {
        return trainDAO.save(train);
    }
    
    /**
     * Met à jour un train
     */
    public Train updateTrain(Train train) {
        return trainDAO.update(train);
    }
    
    /**
     * Supprime un train
     */
    public void deleteTrain(Long id) {
        trainDAO.deleteById(id);
    }
    
    /**
     * Compte le nombre total de trains
     */
    public long getTotalTrainsCount() {
        return trainDAO.count();
    }
    
    /**
     * Compte le nombre de trains actifs
     */
    public long getActiveTrainsCount() {
        return trainDAO.countActiveTrains();
    }
    
    /**
     * Vérifie si un numéro de train existe déjà
     */
    public boolean existsByTrainNumber(String trainNumber) {
        return trainDAO.existsByTrainNumber(trainNumber);
    }
    
    /**
     * Active un train
     */
    public void activateTrain(Long trainId) {
        Optional<Train> trainOpt = trainDAO.findById(trainId);
        if (trainOpt.isPresent()) {
            Train train = trainOpt.get();
            train.setIsActive(true);
            trainDAO.update(train);
        }
    }
    
    /**
     * Désactive un train
     */
    public void deactivateTrain(Long trainId) {
        Optional<Train> trainOpt = trainDAO.findById(trainId);
        if (trainOpt.isPresent()) {
            Train train = trainOpt.get();
            train.setIsActive(false);
            trainDAO.update(train);
        }
    }
    
    /**
     * Recherche des trains par nom
     */
    public List<Train> searchTrainsByName(String name) {
        return trainDAO.findByNameContaining(name);
    }
    
    /**
     * Trouve les trains avec une capacité minimale
     */
    public List<Train> findTrainsWithMinCapacity(int minCapacity) {
        return trainDAO.findByCapacityGreaterThanEqual(minCapacity);
    }
}
