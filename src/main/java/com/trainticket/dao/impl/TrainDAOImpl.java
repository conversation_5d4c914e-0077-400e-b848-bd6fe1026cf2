package com.trainticket.dao.impl;

import com.trainticket.dao.TrainDAO;
import com.trainticket.model.Train;
import com.trainticket.model.TrainType;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;

import java.util.List;
import java.util.Optional;

public class TrainDAOImpl extends GenericDAOImpl<Train, Long> implements TrainDAO {

    @Override
    public Optional<Train> findByTrainNumber(String trainNumber) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Train> query = em.createQuery(
                "SELECT t FROM Train t WHERE t.trainNumber = :trainNumber", Train.class);
            query.setParameter("trainNumber", trainNumber);
            return Optional.of(query.getSingleResult());
        } catch (NoResultException e) {
            return Optional.empty();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Train> findByTrainType(TrainType trainType) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Train> query = em.createQuery(
                "SELECT t FROM Train t WHERE t.trainType = :trainType ORDER BY t.name", Train.class);
            query.setParameter("trainType", trainType);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Train> findActiveTrains() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Train> query = em.createQuery(
                "SELECT t FROM Train t WHERE t.isActive = true ORDER BY t.name", Train.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Train> findByNameContaining(String name) {
        EntityManager em = getEntityManager();
        try {
            String searchPattern = "%" + name.toLowerCase() + "%";
            TypedQuery<Train> query = em.createQuery(
                "SELECT t FROM Train t WHERE LOWER(t.name) LIKE :name ORDER BY t.name", Train.class);
            query.setParameter("name", searchPattern);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public boolean existsByTrainNumber(String trainNumber) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Long> query = em.createQuery(
                "SELECT COUNT(t) FROM Train t WHERE t.trainNumber = :trainNumber", Long.class);
            query.setParameter("trainNumber", trainNumber);
            return query.getSingleResult() > 0;
        } finally {
            em.close();
        }
    }

    @Override
    public List<Train> findByType(TrainType type) {
        return findByTrainType(type); // Alias pour la méthode existante
    }

    @Override
    public List<Train> findByCapacityGreaterThanEqual(int minCapacity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Train> query = em.createQuery(
                "SELECT t FROM Train t WHERE t.capacity >= :minCapacity ORDER BY t.capacity", Train.class);
            query.setParameter("minCapacity", minCapacity);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public long countActiveTrains() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Long> query = em.createQuery(
                "SELECT COUNT(t) FROM Train t WHERE t.isActive = true", Long.class);
            return query.getSingleResult();
        } finally {
            em.close();
        }
    }
}
