package com.trainticket.dao.impl;

import com.trainticket.dao.RouteDAO;
import com.trainticket.model.Route;
import com.trainticket.model.Train;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import java.time.LocalDateTime;
import java.util.List;

public class RouteDAOImpl extends GenericDAOImpl<Route, Long> implements RouteDAO {

    @Override
    public List<Route> findByDepartureAndArrivalCities(String departureCity, String arrivalCity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureCity = :departureCity " +
                "AND r.arrivalCity = :arrivalCity AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", departureCity);
            query.setParameter("arrivalCity", arrivalCity);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDepartureAndArrivalCitiesAndDate(String departureCity, String arrivalCity,
                                                             LocalDateTime startDate, LocalDateTime endDate) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureCity = :departureCity " +
                "AND r.arrivalCity = :arrivalCity AND r.isActive = true " +
                "AND r.departureTime >= :startDate AND r.departureTime <= :endDate " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", departureCity);
            query.setParameter("arrivalCity", arrivalCity);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByTrain(Train train) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.train = :train ORDER BY r.departureTime", Route.class);
            query.setParameter("train", train);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findActiveRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.isActive = true ORDER BY r.departureTime", Route.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findRoutesWithAvailableSeats() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.isActive = true AND r.availableSeats > 0 " +
                "ORDER BY r.departureTime", Route.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDepartureCity(String departureCity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureCity = :departureCity AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", departureCity);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByArrivalCity(String arrivalCity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.arrivalCity = :arrivalCity AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("arrivalCity", arrivalCity);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findFutureRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureTime > :now AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("now", LocalDateTime.now());
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureTime >= :startDate " +
                "AND r.departureTime <= :endDate AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<String> findDistinctDepartureCities() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<String> query = em.createQuery(
                "SELECT DISTINCT r.departureCity FROM Route r WHERE r.isActive = true " +
                "ORDER BY r.departureCity", String.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<String> findDistinctArrivalCities() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<String> query = em.createQuery(
                "SELECT DISTINCT r.arrivalCity FROM Route r WHERE r.isActive = true " +
                "ORDER BY r.arrivalCity", String.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public long countActiveRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Long> query = em.createQuery(
                "SELECT COUNT(r) FROM Route r WHERE r.isActive = true", Long.class);
            return query.getSingleResult();
        } finally {
            em.close();
        }
    }
}
