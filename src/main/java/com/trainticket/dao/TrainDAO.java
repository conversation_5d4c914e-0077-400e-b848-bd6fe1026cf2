package com.trainticket.dao;

import com.trainticket.model.Train;
import com.trainticket.model.TrainType;

import java.util.List;
import java.util.Optional;

public interface TrainDAO extends GenericDAO<Train, Long> {

    /**
     * Trouve un train par son numéro
     */
    Optional<Train> findByTrainNumber(String trainNumber);

    /**
     * Trouve tous les trains par type
     */
    List<Train> findByTrainType(TrainType trainType);

    /**
     * Trouve tous les trains actifs
     */
    List<Train> findActiveTrains();

    /**
     * Trouve les trains par nom (recherche partielle)
     */
    List<Train> findByNameContaining(String name);

    /**
     * Vérifie si un numéro de train existe déjà
     */
    boolean existsByTrainNumber(String trainNumber);

    /**
     * Trouve les trains par type (alias pour findByTrainType)
     */
    List<Train> findByType(TrainType type);

    /**
     * Trouve les trains avec une capacité minimale
     */
    List<Train> findByCapacityGreaterThanEqual(int minCapacity);

    /**
     * Compte le nombre de trains actifs
     */
    long countActiveTrains();
}
