package com.trainticket.dao;

import com.trainticket.model.User;
import com.trainticket.model.UserRole;

import java.util.List;
import java.util.Optional;

public interface UserDAO extends GenericDAO<User, Long> {

    /**
     * Trouve un utilisateur par son nom d'utilisateur
     */
    Optional<User> findByUsername(String username);

    /**
     * Trouve un utilisateur par son email
     */
    Optional<User> findByEmail(String email);

    /**
     * Trouve un utilisateur par son nom d'utilisateur ou email
     */
    Optional<User> findByUsernameOrEmail(String usernameOrEmail);

    /**
     * Trouve tous les utilisateurs par rôle
     */
    List<User> findByRole(UserRole role);

    /**
     * Trouve tous les utilisateurs actifs
     */
    List<User> findActiveUsers();

    /**
     * Vérifie si un nom d'utilisateur existe déjà
     */
    boolean existsByUsername(String username);

    /**
     * Vérifie si un email existe déjà
     */
    boolean existsByEmail(String email);

    /**
     * Trouve les utilisateurs par nom ou prénom (recherche partielle)
     */
    List<User> findByNameContaining(String name);

    /**
     * Compte le nombre d'utilisateurs par rôle
     */
    long countByRole(UserRole role);

    /**
     * Trouve les utilisateurs récents
     */
    List<User> findRecentUsers(int limit);
}
