<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainTickets - Réservation de Billets de Train</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #1e40af;
            --accent-color: #312e81;
            --orange-color: #c2410c;
            --orange-light: #ea580c;
            --orange-dark: #9a3412;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #0f172a;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #ffffff;
        }

        /* Header Styles */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            box-shadow: 0 4px 20px rgba(30, 58, 138, 0.25);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background-color: var(--orange-color);
            color: white;
            border-color: var(--orange-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        .btn-light {
            background-color: var(--orange-color);
            border-color: var(--orange-color);
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-light:hover {
            background-color: var(--orange-dark);
            border-color: var(--orange-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(154, 52, 18, 0.3);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-hero {
            background: var(--orange-color);
            color: white;
            font-weight: 600;
            padding: 1rem 2rem;
            border-radius: 50px;
            border: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        .btn-hero:hover {
            background: var(--orange-dark);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(154, 52, 18, 0.4);
            color: white;
        }

        /* Features Section */
        .features-section {
            padding: 5rem 0;
            background-color: var(--light-color);
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--orange-color), var(--orange-light));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        /* Pricing Section */
        .pricing-section {
            padding: 5rem 0;
            background: white;
        }

        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            height: 100%;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .pricing-card.featured {
            border-color: var(--orange-color);
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(194, 65, 12, 0.2);
        }

        .pricing-card.featured::before {
            content: 'POPULAIRE';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--orange-color);
            color: white;
            padding: 0.5rem 2rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        .price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 1rem 0;
        }

        .price-unit {
            font-size: 1rem;
            color: #6b7280;
            font-weight: 400;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, var(--dark-color) 0%, #374151 100%);
            color: white;
            padding: 5rem 0;
            text-align: center;
        }

        /* Footer */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer h5 {
            color: white;
            margin-bottom: 1rem;
        }

        .footer a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .pricing-card.featured {
                transform: none;
                margin-top: 2rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train me-2"></i>TrainTickets
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tarifs">Tarifs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>

                <div class="d-flex">
                    <a href="${pageContext.request.contextPath}/login" class="btn btn-outline-light me-2">
                        <i class="fas fa-sign-in-alt me-1"></i>Connexion
                    </a>
                    <a href="${pageContext.request.contextPath}/register" class="btn btn-light">
                        <i class="fas fa-user-plus me-1"></i>Inscription
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <h1 class="hero-title animate-fade-in-up">
                        Voyagez en Train<br>
                        <span style="color: var(--orange-light);">Simplement</span>
                    </h1>
                    <p class="hero-subtitle animate-fade-in-up">
                        Réservez vos billets de train en quelques clics.
                        Des milliers de destinations, des tarifs avantageux,
                        et un service client exceptionnel.
                    </p>
                    <div class="animate-fade-in-up">
                        <a href="${pageContext.request.contextPath}/search" class="btn btn-hero me-3">
                            <i class="fas fa-search me-2"></i>Rechercher un Trajet
                        </a>
                        <a href="#tarifs" class="btn btn-outline-light">
                            <i class="fas fa-info-circle me-2"></i>Découvrir nos Tarifs
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <i class="fas fa-train" style="font-size: 15rem; opacity: 0.1;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="features-section" id="services">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold text-dark mb-3">Pourquoi Choisir TrainTickets ?</h2>
                    <p class="lead text-muted">Des services pensés pour votre confort et votre tranquillité</p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Réservation Instantanée</h4>
                        <p class="text-muted">
                            Réservez vos billets en moins de 2 minutes.
                            Confirmation immédiate par email et SMS.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Paiement Sécurisé</h4>
                        <p class="text-muted">
                            Transactions 100% sécurisées avec cryptage SSL.
                            Acceptation de toutes les cartes bancaires.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Support 24/7</h4>
                        <p class="text-muted">
                            Notre équipe est disponible 24h/24 et 7j/7
                            pour vous accompagner dans vos voyages.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Application Mobile</h4>
                        <p class="text-muted">
                            Gérez vos réservations depuis votre smartphone.
                            Billets électroniques et notifications en temps réel.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-route"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Réseau National</h4>
                        <p class="text-muted">
                            Plus de 3000 destinations dans toute la France.
                            TGV, Intercités, TER et trains internationaux.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-percent"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Meilleurs Prix</h4>
                        <p class="text-muted">
                            Garantie du meilleur prix. Réductions exclusives
                            et offres spéciales pour nos membres fidèles.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section" id="tarifs">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold text-dark mb-3">Nos Tarifs</h2>
                    <p class="lead text-muted">Des prix adaptés à tous vos besoins de voyage</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- Tarif Économique -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card">
                        <div class="mb-4">
                            <i class="fas fa-train text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h3 class="fw-bold mb-3">Économique</h3>
                        <div class="price">
                            25€
                            <span class="price-unit">/ trajet</span>
                        </div>
                        <p class="text-muted mb-4">Parfait pour les trajets courts et les budgets serrés</p>

                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Réservation en ligne</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Billet électronique</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Modification gratuite</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Siège standard</li>
                            <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>Repas inclus</li>
                            <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>WiFi premium</li>
                        </ul>

                        <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-primary w-100">
                            Choisir ce tarif
                        </a>
                    </div>
                </div>

                <!-- Tarif Confort -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card featured">
                        <div class="mb-4">
                            <i class="fas fa-star text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h3 class="fw-bold mb-3">Confort</h3>
                        <div class="price">
                            45€
                            <span class="price-unit">/ trajet</span>
                        </div>
                        <p class="text-muted mb-4">Le meilleur rapport qualité-prix pour vos voyages</p>

                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Réservation en ligne</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Billet électronique</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Modification gratuite</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Siège confort</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Collation incluse</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>WiFi gratuit</li>
                        </ul>

                        <a href="${pageContext.request.contextPath}/search" class="btn btn-primary w-100">
                            Choisir ce tarif
                        </a>
                    </div>
                </div>

                <!-- Tarif Premium -->
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card">
                        <div class="mb-4">
                            <i class="fas fa-crown text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h3 class="fw-bold mb-3">Premium</h3>
                        <div class="price">
                            75€
                            <span class="price-unit">/ trajet</span>
                        </div>
                        <p class="text-muted mb-4">L'excellence du voyage en première classe</p>

                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Réservation prioritaire</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Billet flexible</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Annulation gratuite</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Siège première classe</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Repas gastronomique</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>WiFi premium + prise</li>
                        </ul>

                        <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-primary w-100">
                            Choisir ce tarif
                        </a>
                    </div>
                </div>
            </div>

            <!-- Informations supplémentaires -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="alert alert-info">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="alert-heading mb-2">
                                    <i class="fas fa-info-circle me-2"></i>Informations Tarifaires
                                </h5>
                                <p class="mb-0">
                                    Les prix indiqués sont des exemples pour un trajet Paris-Lyon.
                                    Les tarifs varient selon la destination, l'heure et la période de réservation.
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <a href="${pageContext.request.contextPath}/search" class="btn btn-primary">
                                    <i class="fas fa-calculator me-2"></i>Calculer mon tarif
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Prêt à Partir ?</h2>
                    <p class="lead mb-4">
                        Rejoignez plus de 2 millions de voyageurs qui nous font confiance.
                        Réservez votre prochain voyage dès maintenant !
                    </p>
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <a href="${pageContext.request.contextPath}/register" class="btn btn-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Créer un Compte Gratuit
                        </a>
                        <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-search me-2"></i>Rechercher un Trajet
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-train me-2"></i>TrainTickets
                    </h5>
                    <p class="text-muted">
                        Votre partenaire de confiance pour tous vos voyages en train.
                        Simplicité, rapidité et sécurité garanties.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-muted"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-muted"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-muted"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-muted"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="fw-bold mb-3">Services</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="${pageContext.request.contextPath}/search">Recherche</a></li>
                        <li class="mb-2"><a href="${pageContext.request.contextPath}/login">Mes Réservations</a></li>
                        <li class="mb-2"><a href="#">Horaires</a></li>
                        <li class="mb-2"><a href="#">Cartes & Abonnements</a></li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="fw-bold mb-3">Aide</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#">FAQ</a></li>
                        <li class="mb-2"><a href="#">Contact</a></li>
                        <li class="mb-2"><a href="#">Remboursements</a></li>
                        <li class="mb-2"><a href="#">Réclamations</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">Contact</h5>
                    <div class="mb-3">
                        <i class="fas fa-phone me-2"></i>
                        <span>+33 1 23 45 67 89</span>
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-envelope me-2"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span>123 Avenue des Trains, 75001 Paris</span>
                    </div>
                    <div>
                        <i class="fas fa-clock me-2"></i>
                        <span>Service client 24h/24 - 7j/7</span>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; 2024 TrainTickets. Tous droits réservés.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted me-3">Mentions légales</a>
                    <a href="#" class="text-muted me-3">Politique de confidentialité</a>
                    <a href="#" class="text-muted">CGV</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Smooth scrolling pour les liens d'ancrage
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animation au scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observer les cartes de fonctionnalités et de tarifs
        document.querySelectorAll('.feature-card, .pricing-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>