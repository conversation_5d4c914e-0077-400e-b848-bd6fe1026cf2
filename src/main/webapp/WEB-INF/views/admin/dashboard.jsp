<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - TrainTickets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #1e40af;
            --accent-color: #312e81;
            --orange-color: #c2410c;
            --orange-light: #ea580c;
            --orange-dark: #9a3412;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #0f172a;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            box-shadow: 0 4px 20px rgba(30, 58, 138, 0.25);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            color: var(--orange-light) !important;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .stat-icon.users { background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); }
        .stat-icon.trains { background: linear-gradient(135deg, var(--orange-color), var(--orange-light)); }
        .stat-icon.routes { background: linear-gradient(135deg, var(--success-color), #34d399); }
        .stat-icon.active { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .stat-change {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .stat-change.positive { color: var(--success-color); }
        .stat-change.neutral { color: var(--warning-color); }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem;
            font-weight: 600;
        }

        .btn-admin {
            background: var(--orange-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            background: var(--orange-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background-color: #e5e7eb;
        }

        .progress-bar-custom {
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .badge-role {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .badge-admin { background: var(--danger-color); color: white; }
        .badge-employee { background: var(--warning-color); color: white; }
        .badge-client { background: var(--success-color); color: white; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train me-2"></i>TrainTickets Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/trains">
                            <i class="fas fa-train me-1"></i>Trains
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/routes">
                            <i class="fas fa-route me-1"></i>Trajets
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-shield me-1"></i>
                        Bonjour, ${currentUser.firstName}
                    </span>
                    <a href="${pageContext.request.contextPath}/logout" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Admin -->
    <section class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-tachometer-alt me-3"></i>Dashboard Administrateur
                    </h1>
                    <p class="mb-0 opacity-75">
                        Vue d'ensemble de la plateforme TrainTickets
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="text-white">
                        <i class="fas fa-calendar me-2"></i>
                        <span id="currentDate"></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistiques Principales -->
    <div class="container mb-5">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">${totalUsers}</div>
                    <div class="stat-label">Utilisateurs Total</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up me-1"></i>Actif
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon trains">
                        <i class="fas fa-train"></i>
                    </div>
                    <div class="stat-number">${totalTrains}</div>
                    <div class="stat-label">Trains Disponibles</div>
                    <div class="stat-change neutral">
                        <i class="fas fa-minus me-1"></i>Stable
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon routes">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="stat-number">${totalRoutes}</div>
                    <div class="stat-label">Trajets Total</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up me-1"></i>En croissance
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon active">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">${activeRoutes}</div>
                    <div class="stat-label">Trajets Actifs</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up me-1"></i>${activeRoutesPercentage}%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Répartition des Utilisateurs et Actions Rapides -->
    <div class="container mb-5">
        <div class="row g-4">
            <!-- Répartition des Utilisateurs -->
            <div class="col-lg-6">
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-chart-pie me-2"></i>Répartition des Utilisateurs
                    </div>
                    <div class="p-4">
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge-role badge-admin">Administrateurs</span>
                                <strong>${adminCount}</strong>
                            </div>
                            <div class="progress-custom">
                                <div class="progress-bar-custom bg-danger" style="width: ${adminPercentage}%"></div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge-role badge-employee">Employés</span>
                                <strong>${employeeCount}</strong>
                            </div>
                            <div class="progress-custom">
                                <div class="progress-bar-custom bg-warning" style="width: ${employeePercentage}%"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge-role badge-client">Clients</span>
                                <strong>${clientCount}</strong>
                            </div>
                            <div class="progress-custom">
                                <div class="progress-bar-custom bg-success" style="width: ${clientPercentage}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions Rapides -->
            <div class="col-lg-6">
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-bolt me-2"></i>Actions Rapides
                    </div>
                    <div class="p-4">
                        <div class="d-grid gap-3">
                            <a href="${pageContext.request.contextPath}/admin/users/add" class="btn btn-admin">
                                <i class="fas fa-user-plus me-2"></i>Ajouter un Utilisateur
                            </a>
                            <a href="${pageContext.request.contextPath}/admin/trains/add" class="btn btn-admin">
                                <i class="fas fa-train me-2"></i>Ajouter un Train
                            </a>
                            <a href="${pageContext.request.contextPath}/admin/routes/add" class="btn btn-admin">
                                <i class="fas fa-route me-2"></i>Ajouter un Trajet
                            </a>
                            <a href="${pageContext.request.contextPath}/admin/reports" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar me-2"></i>Voir les Rapports
                            </a>
                            <a href="${pageContext.request.contextPath}/admin/settings" class="btn btn-outline-secondary">
                                <i class="fas fa-cog me-2"></i>Paramètres Système
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Utilisateurs Récents et Trajets Populaires -->
    <div class="container mb-5">
        <div class="row g-4">
            <!-- Utilisateurs Récents -->
            <div class="col-lg-6">
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-user-clock me-2"></i>Utilisateurs Récents
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Rôle</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="user" items="${recentUsers}">
                                    <tr>
                                        <td>
                                            <strong>${user.firstName} ${user.lastName}</strong>
                                        </td>
                                        <td>${user.email}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${user.role == 'ADMIN'}">
                                                    <span class="badge bg-danger">Admin</span>
                                                </c:when>
                                                <c:when test="${user.role == 'EMPLOYEE'}">
                                                    <span class="badge bg-warning">Employé</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge bg-success">Client</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                ${user.createdAt.toLocalDate()}
                                            </small>
                                        </td>
                                    </tr>
                                </c:forEach>
                                <c:if test="${empty recentUsers}">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            Aucun utilisateur récent
                                        </td>
                                    </tr>
                                </c:if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Trajets Populaires -->
            <div class="col-lg-6">
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-star me-2"></i>Trajets Populaires
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Trajet</th>
                                    <th>Prix</th>
                                    <th>Places</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="route" items="${popularRoutes}">
                                    <tr>
                                        <td>
                                            <strong>${route.departureCity}</strong>
                                            <i class="fas fa-arrow-right mx-2 text-muted"></i>
                                            <strong>${route.arrivalCity}</strong>
                                        </td>
                                        <td>
                                            <span class="text-primary fw-bold">${route.price}€</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">${route.availableSeats}</span>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${route.isActive}">
                                                    <span class="badge bg-success">Actif</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge bg-secondary">Inactif</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </tr>
                                </c:forEach>
                                <c:if test="${empty popularRoutes}">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            Aucun trajet populaire
                                        </td>
                                    </tr>
                                </c:if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; 2024 TrainTickets Admin Panel. Tous droits réservés.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        Version 1.0.0 | Dernière mise à jour: <span id="lastUpdate"></span>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Afficher la date actuelle
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Afficher l'heure de dernière mise à jour
        document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('fr-FR');

        // Animation des barres de progression
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.progress-bar-custom');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });

        // Actualisation automatique des statistiques (optionnel)
        setInterval(function() {
            // Ici vous pourriez ajouter une requête AJAX pour actualiser les stats
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('fr-FR');
        }, 60000); // Toutes les minutes
    </script>
</body>
</html>
