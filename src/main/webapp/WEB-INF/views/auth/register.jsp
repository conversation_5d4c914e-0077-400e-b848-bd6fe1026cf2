<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - TrainTickets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #1e40af;
            --accent-color: #312e81;
            --orange-color: #c2410c;
            --orange-light: #ea580c;
            --orange-dark: #9a3412;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px 0;
        }
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2.5rem;
            text-align: center;
        }
        .register-body {
            padding: 2.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        .btn-outline-primary, .btn-outline-secondary {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-outline-primary:hover, .btn-outline-secondary:hover {
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--orange-color);
            border-color: var(--orange-color);
            color: white;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-warning:hover {
            background: var(--orange-dark);
            border-color: var(--orange-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(154, 52, 18, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-container">
                    <div class="register-header">
                        <h2 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>Inscription
                        </h2>
                        <p class="mb-0 mt-2">Créez votre compte TrainTickets</p>
                    </div>

                    <div class="register-body">
                        <!-- Messages d'erreur -->
                        <c:if test="${error != null}">
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>${error}
                            </div>
                        </c:if>

                        <!-- Messages de succès -->
                        <c:if test="${success != null}">
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>${success}
                            </div>
                        </c:if>

                        <!-- Formulaire d'inscription -->
                        <form action="${pageContext.request.contextPath}/register" method="post" id="registerForm" novalidate>
                            <input type="hidden" name="action" value="register">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">
                                        <i class="fas fa-user me-1"></i>Prénom *
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="firstName"
                                           name="firstName"
                                           value="${firstName != null ? firstName : ''}"
                                           placeholder="Votre prénom"
                                           required>
                                    <div class="invalid-feedback">
                                        Veuillez saisir votre prénom.
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">
                                        <i class="fas fa-user me-1"></i>Nom *
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="lastName"
                                           name="lastName"
                                           value="${lastName != null ? lastName : ''}"
                                           placeholder="Votre nom"
                                           required>
                                    <div class="invalid-feedback">
                                        Veuillez saisir votre nom.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-at me-1"></i>Nom d'utilisateur *
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="username"
                                       name="username"
                                       value="${username != null ? username : ''}"
                                       placeholder="Choisissez un nom d'utilisateur"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez choisir un nom d'utilisateur.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email *
                                </label>
                                <input type="email"
                                       class="form-control"
                                       id="email"
                                       name="email"
                                       value="${email != null ? email : ''}"
                                       placeholder="<EMAIL>"
                                       required>
                                <div class="invalid-feedback">
                                    Veuillez saisir un email valide.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="phoneNumber" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Téléphone
                                </label>
                                <input type="tel"
                                       class="form-control"
                                       id="phoneNumber"
                                       name="phoneNumber"
                                       value="${phoneNumber != null ? phoneNumber : ''}"
                                       placeholder="+33 6 12 34 56 78">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Mot de passe *
                                    </label>
                                    <input type="password"
                                           class="form-control"
                                           id="password"
                                           name="password"
                                           placeholder="Choisissez un mot de passe"
                                           required>
                                    <div class="invalid-feedback">
                                        Veuillez choisir un mot de passe.
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirmPassword" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Confirmer *
                                    </label>
                                    <input type="password"
                                           class="form-control"
                                           id="confirmPassword"
                                           name="confirmPassword"
                                           placeholder="Confirmez le mot de passe"
                                           required>
                                    <div class="invalid-feedback">
                                        Veuillez confirmer votre mot de passe.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="acceptTerms" name="acceptTerms" required>
                                <label class="form-check-label" for="acceptTerms">
                                    J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a> *
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary" id="registerButton">
                                    <i class="fas fa-user-plus me-2"></i>Créer mon compte
                                </button>
                            </div>
                        </form>

                        <!-- Liens -->
                        <div class="text-center">
                            <p class="mb-2">Vous avez déjà un compte ?</p>
                            <a href="${pageContext.request.contextPath}/login" class="btn btn-warning me-2">
                                <i class="fas fa-sign-in-alt me-1"></i>Se connecter
                            </a>
                            <a href="${pageContext.request.contextPath}/" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-1"></i>Accueil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript pour la validation et les fonctionnalités -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔍 Page d\'inscription chargée');

        const registerForm = document.getElementById('registerForm');
        const registerButton = document.getElementById('registerButton');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const firstNameInput = document.getElementById('firstName');
        const lastNameInput = document.getElementById('lastName');
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');
        const acceptTermsInput = document.getElementById('acceptTerms');

        // Validation en temps réel
        [firstNameInput, lastNameInput, usernameInput, emailInput, passwordInput, confirmPasswordInput].forEach(input => {
            if (input) {
                input.addEventListener('input', function() {
                    validateField(this);
                });
            }
        });

        // Validation spéciale pour la confirmation du mot de passe
        confirmPasswordInput.addEventListener('input', function() {
            validatePasswordMatch();
        });

        passwordInput.addEventListener('input', function() {
            if (confirmPasswordInput.value) {
                validatePasswordMatch();
            }
        });

        // Gestion de la soumission du formulaire
        registerForm.addEventListener('submit', function(e) {
            console.log('🔍 Tentative de soumission du formulaire d\'inscription');

            // Validation côté client
            if (!validateForm()) {
                console.log('❌ Validation côté client échouée');
                e.preventDefault();
                return false;
            }

            console.log('✅ Validation côté client réussie');

            // Désactiver le bouton et afficher le spinner
            registerButton.disabled = true;
            registerButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création en cours...';

            // Réactiver le bouton après 10 secondes en cas de problème
            setTimeout(function() {
                if (registerButton.disabled) {
                    console.log('⚠️ Timeout - Réactivation du bouton');
                    resetRegisterButton();
                }
            }, 10000);
        });

        // Fonction pour réinitialiser le bouton d'inscription
        function resetRegisterButton() {
            registerButton.disabled = false;
            registerButton.innerHTML = '<i class="fas fa-user-plus me-2"></i>Créer mon compte';
        }

        // Fonction de validation d'un champ
        function validateField(field) {
            const value = field.value.trim();

            if (field.hasAttribute('required') && value === '') {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                return false;
            } else if (field.type === 'email' && value && !isValidEmail(value)) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                return false;
            } else if (value || !field.hasAttribute('required')) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
                return true;
            }
            return true;
        }

        // Validation de la correspondance des mots de passe
        function validatePasswordMatch() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (confirmPassword && password !== confirmPassword) {
                confirmPasswordInput.classList.add('is-invalid');
                confirmPasswordInput.classList.remove('is-valid');
                confirmPasswordInput.nextElementSibling.textContent = 'Les mots de passe ne correspondent pas.';
                return false;
            } else if (confirmPassword) {
                confirmPasswordInput.classList.remove('is-invalid');
                confirmPasswordInput.classList.add('is-valid');
                confirmPasswordInput.nextElementSibling.textContent = 'Veuillez confirmer votre mot de passe.';
                return true;
            }
            return true;
        }

        // Validation email
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Fonction de validation du formulaire complet
        function validateForm() {
            let isValid = true;

            // Validation des champs obligatoires
            [firstNameInput, lastNameInput, usernameInput, emailInput, passwordInput, confirmPasswordInput].forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            // Validation de la correspondance des mots de passe
            if (!validatePasswordMatch()) {
                isValid = false;
            }

            // Validation des conditions d'utilisation
            if (!acceptTermsInput.checked) {
                console.log('❌ Conditions d\'utilisation non acceptées');
                acceptTermsInput.classList.add('is-invalid');
                isValid = false;
            } else {
                acceptTermsInput.classList.remove('is-invalid');
            }

            return isValid;
        }

        // Log des erreurs existantes
        <c:if test="${error != null}">
            console.error('❌ Erreur d\'inscription:', '${error}');
        </c:if>

        console.log('✅ Page d\'inscription prête');
    });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
