<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="pageTitle" value="Réservation - TrainTickets" scope="request"/>
<jsp:include page="/WEB-INF/views/layout/header.jsp"/>

<div class="row">
    <!-- Informations du trajet -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i>Réservation de billet
                </h4>
            </div>
            <div class="card-body">
                <!-- Messages d'erreur -->
                <c:if test="${error != null}">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>${error}
                    </div>
                </c:if>

                <!-- Détails du trajet -->
                <div class="card bg-light mb-4">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-train text-primary me-2"></i>
                            ${route.train.name} - ${route.train.trainType.displayName}
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="text-center me-3">
                                        <strong>${route.departureCity}</strong><br>
                                        <small class="text-muted">Départ</small>
                                    </div>
                                    <div class="mx-3">
                                        <i class="fas fa-arrow-right text-primary"></i>
                                    </div>
                                    <div class="text-center">
                                        <strong>${route.arrivalCity}</strong><br>
                                        <small class="text-muted">Arrivée</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <i class="fas fa-calendar me-2"></i>
                                    <fmt:formatDate value="${route.departureTime}" pattern="EEEE dd MMMM yyyy"/>
                                </p>
                                <p class="mb-1">
                                    <i class="fas fa-clock me-2"></i>
                                    <fmt:formatDate value="${route.departureTime}" pattern="HH:mm"/> - 
                                    <fmt:formatDate value="${route.arrivalTime}" pattern="HH:mm"/>
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    ${passengerCount} passager(s)
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de réservation -->
                <form action="${pageContext.request.contextPath}/reservation/book" method="post" id="bookingForm">
                    <input type="hidden" name="routeId" value="${route.id}">
                    <input type="hidden" name="passengerCount" value="${passengerCount}">

                    <div class="mb-4">
                        <label for="passengerNames" class="form-label">
                            <i class="fas fa-users me-1"></i>Noms des passagers *
                        </label>
                        <textarea class="form-control" id="passengerNames" name="passengerNames" 
                                  rows="3" required placeholder="Entrez les noms complets des passagers, un par ligne">${passengerNames}</textarea>
                        <div class="form-text">
                            Veuillez entrer les noms complets de tous les passagers (${passengerCount} passager(s))
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="specialRequests" class="form-label">
                            <i class="fas fa-comment me-1"></i>Demandes spéciales (optionnel)
                        </label>
                        <textarea class="form-control" id="specialRequests" name="specialRequests" 
                                  rows="2" placeholder="Assistance mobilité, régime alimentaire, etc.">${specialRequests}</textarea>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="acceptConditions" required>
                            <label class="form-check-label" for="acceptConditions">
                                J'accepte les <a href="#" target="_blank">conditions de vente</a> 
                                et les <a href="#" target="_blank">conditions d'annulation</a> *
                            </label>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check me-2"></i>Confirmer la réservation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Récapitulatif -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>Récapitulatif
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Trajet</h6>
                    <p class="mb-1">${route.departureCity} → ${route.arrivalCity}</p>
                    <small class="text-muted">
                        <fmt:formatDate value="${route.departureTime}" pattern="dd/MM/yyyy à HH:mm"/>
                    </small>
                </div>

                <div class="mb-3">
                    <h6>Train</h6>
                    <p class="mb-0">${route.train.name}</p>
                    <small class="text-muted">${route.train.trainType.displayName}</small>
                </div>

                <div class="mb-3">
                    <h6>Passagers</h6>
                    <p class="mb-0">${passengerCount} personne(s)</p>
                </div>

                <hr>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Prix unitaire :</span>
                        <span><fmt:formatNumber value="${route.price}" type="currency" currencySymbol="€"/></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Quantité :</span>
                        <span>× ${passengerCount}</span>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between mb-3">
                    <strong>Total :</strong>
                    <strong class="text-success">
                        <fmt:formatNumber value="${route.price * passengerCount}" type="currency" currencySymbol="€"/>
                    </strong>
                </div>

                <div class="alert alert-info" role="alert">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Votre réservation sera valable 24h. Vous devrez procéder au paiement pour confirmer votre billet.
                    </small>
                </div>
            </div>
        </div>

        <!-- Informations utilisateur -->
        <div class="card shadow mt-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Informations du compte
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>${currentUser.firstName} ${currentUser.lastName}</strong></p>
                <p class="mb-1">${currentUser.email}</p>
                <c:if test="${currentUser.phoneNumber != null}">
                    <p class="mb-0">${currentUser.phoneNumber}</p>
                </c:if>
            </div>
        </div>
    </div>
</div>

<script>
// Validation du formulaire
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    const passengerNames = document.getElementById('passengerNames').value.trim();
    const passengerCount = ${passengerCount};
    
    if (!passengerNames) {
        e.preventDefault();
        alert('Veuillez entrer les noms des passagers');
        return false;
    }
    
    // Vérifier le nombre de lignes (approximatif)
    const lines = passengerNames.split('\n').filter(line => line.trim().length > 0);
    if (lines.length !== passengerCount) {
        e.preventDefault();
        alert(`Veuillez entrer exactement ${passengerCount} nom(s) de passager(s), un par ligne`);
        return false;
    }
});

// Auto-resize du textarea
document.getElementById('passengerNames').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>

<jsp:include page="/WEB-INF/views/layout/footer.jsp"/>
