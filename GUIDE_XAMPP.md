# 🗄️ Guide Configuration XAMPP pour TrainTickets

## 📋 Étapes de Configuration

### 1. **Installation de XAMPP**

#### Téléchargement
- Allez sur https://www.apachefriends.org/
- Téléchargez XAMPP pour Windows
- Installez dans `C:\xampp` (recommandé)

#### Démarrage des Services
1. Ouvrez **XAMPP Control Panel** en tant qu'administrateur
2. Cliquez sur **Start** pour :
   - ✅ **Apache** (serveur web)
   - ✅ **MySQL** (base de données)
3. Vérifiez que les voyants sont **verts**

### 2. **Création de la Base de Données**

#### Via phpMyAdmin (Recommandé)
1. Ouvrez votre navigateur
2. Allez sur : http://localhost/phpmyadmin
3. Cliquez sur **"Nouvelle base de données"**
4. Nom : `trainticket_db`
5. Interclassement : `utf8mb4_unicode_ci`
6. <PERSON><PERSON><PERSON> **"Créer"**

#### Via SQL (Alternative)
```sql
CREATE DATABASE trainticket_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 3. **Configuration du Projet**

#### Fichiers Modifiés
- ✅ `src/main/resources/META-INF/persistence.xml`
- ✅ `pom.xml` (dépendance MySQL)

#### Nouvelle Configuration
```xml
<!-- MySQL avec XAMPP -->
<property name="jakarta.persistence.jdbc.url" 
          value="**************************************************************************"/>
<property name="jakarta.persistence.jdbc.user" value="root"/>
<property name="jakarta.persistence.jdbc.password" value=""/>
```

### 4. **Test de la Configuration**

#### Vérification XAMPP
```bash
# Vérifier que MySQL fonctionne
mysql -u root -p
# (Mot de passe vide par défaut)

# Lister les bases de données
SHOW DATABASES;

# Utiliser notre base
USE trainticket_db;

# Lister les tables (vide au début)
SHOW TABLES;
```

#### Test de l'Application
1. Compiler le projet : `mvn clean compile`
2. Lancer l'application : `mvn jetty:run`
3. Vérifier les logs pour la connexion MySQL
4. Accéder à : http://localhost:8081/train-tickets/test

### 5. **Avantages de MySQL vs H2**

#### ✅ **Avec MySQL (XAMPP)**
- 💾 **Données persistantes** (ne se perdent pas au redémarrage)
- 🔍 **Interface phpMyAdmin** pour visualiser les données
- 🚀 **Performance** meilleure pour de gros volumes
- 🔧 **Configuration réaliste** (comme en production)
- 📊 **Outils de monitoring** intégrés

#### ❌ **Avec H2 (mémoire)**
- 💨 Données perdues à chaque redémarrage
- 🔍 Interface limitée
- 📈 Moins performant pour de gros volumes

### 6. **Utilisation de phpMyAdmin**

#### Accès
- URL : http://localhost/phpmyadmin
- Utilisateur : `root`
- Mot de passe : (vide)

#### Fonctionnalités Utiles
- 📊 **Parcourir** : Voir les données des tables
- 🔍 **SQL** : Exécuter des requêtes personnalisées
- 📈 **Structure** : Voir la structure des tables
- 📤 **Exporter** : Sauvegarder la base de données
- 📥 **Importer** : Restaurer une sauvegarde

### 7. **Surveillance des Données**

#### Voir les Tables Créées
Après le premier lancement de l'application :
```sql
USE trainticket_db;
SHOW TABLES;

-- Vous devriez voir :
-- - users
-- - trains  
-- - routes
-- - tickets
-- - reservations
-- - payments
```

#### Voir les Données de Test
```sql
-- Compter les utilisateurs
SELECT COUNT(*) as nb_users FROM users;

-- Voir les trains
SELECT * FROM trains;

-- Voir quelques trajets
SELECT * FROM routes LIMIT 10;

-- Voir les utilisateurs
SELECT username, email, role FROM users;
```

### 8. **Dépannage**

#### Problème : MySQL ne démarre pas
**Solutions :**
1. Vérifier qu'aucun autre MySQL n'est installé
2. Changer le port MySQL dans XAMPP (3307 au lieu de 3306)
3. Redémarrer XAMPP en tant qu'administrateur

#### Problème : Erreur de connexion
**Vérifications :**
1. XAMPP MySQL est démarré (voyant vert)
2. Base de données `trainticket_db` existe
3. Configuration dans `persistence.xml` est correcte

#### Problème : Tables non créées
**Solutions :**
1. Vérifier les logs de l'application
2. S'assurer que `hibernate.hbm2ddl.auto=update`
3. Redémarrer l'application

### 9. **Commandes Utiles**

#### Démarrage/Arrêt XAMPP
```bash
# Via Control Panel ou en ligne de commande
cd C:\xampp
xampp_start.exe
xampp_stop.exe
```

#### Sauvegarde de la Base
```bash
# Exporter la base de données
mysqldump -u root trainticket_db > backup.sql

# Importer une sauvegarde
mysql -u root trainticket_db < backup.sql
```

### 10. **Sécurité (Optionnel)**

#### Créer un Utilisateur Dédié
```sql
-- Créer un utilisateur pour l'application
CREATE USER 'trainticket_user'@'localhost' IDENTIFIED BY 'motdepasse123';

-- Donner les permissions
GRANT ALL PRIVILEGES ON trainticket_db.* TO 'trainticket_user'@'localhost';
FLUSH PRIVILEGES;
```

Puis modifier `persistence.xml` :
```xml
<property name="jakarta.persistence.jdbc.user" value="trainticket_user"/>
<property name="jakarta.persistence.jdbc.password" value="motdepasse123"/>
```

## 🎯 **Checklist de Configuration**

- [ ] ✅ XAMPP installé et démarré
- [ ] ✅ MySQL fonctionne (voyant vert)
- [ ] ✅ phpMyAdmin accessible
- [ ] ✅ Base `trainticket_db` créée
- [ ] ✅ `persistence.xml` modifié pour MySQL
- [ ] ✅ Dépendance MySQL dans `pom.xml`
- [ ] ✅ Application compile sans erreur
- [ ] ✅ Application se connecte à MySQL
- [ ] ✅ Tables créées automatiquement
- [ ] ✅ Données de test insérées
- [ ] ✅ Interface accessible

## 🎉 **Résultat Final**

Avec cette configuration, vous aurez :
- 🗄️ Une vraie base de données MySQL
- 💾 Des données qui persistent entre les redémarrages
- 🔍 Une interface graphique pour voir vos données
- 🚀 Une configuration proche de la production

**Prêt pour le développement professionnel !** 🚀
