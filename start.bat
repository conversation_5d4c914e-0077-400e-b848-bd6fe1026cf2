@echo off
chcp 65001 >nul

echo 🚂 Démarrage de TrainTickets...

REM Vérifier que Java est installé
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java n'est pas installé. Veuillez installer Java 17 ou supérieur.
    pause
    exit /b 1
)

REM Vérifier que Maven est installé
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven n'est pas installé. Veuillez installer Maven 3.6 ou supérieur.
    pause
    exit /b 1
)

echo ✅ Java et Maven sont installés

REM Nettoyer et compiler le projet
echo 🔧 Compilation du projet...
call mvn clean compile

if %errorlevel% neq 0 (
    echo ❌ Erreur lors de la compilation
    pause
    exit /b 1
)

echo ✅ Compilation réussie

REM Démarrer l'application
echo 🚀 Démarrage de l'application...
echo 📍 L'application sera accessible à: http://localhost:8080/train-tickets
echo ⏹️  Pour arrêter l'application, appuyez sur Ctrl+C
echo.
echo 👥 Comptes de test disponibles:
echo    Admin: <EMAIL> / password123
echo    Client: <EMAIL> / password123
echo.

call mvn jetty:run

pause
